import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { Id } from "../../../../../../convex/_generated/dataModel";
import { useClerk } from "@clerk/clerk-react";

/**
 * Hook personnalisé pour gérer l'état et la logique de la sidebar améliorée
 * pour l'interface Claude
 */
export const useEnhancedSidebar = (
  currentConversationId: Id<"conversations"> | null,
  onSelectConversation: (id: Id<"conversations">) => void,
  onNewConversation: () => void
) => {
  // Mutations Convex
  const renameConversation = useMutation(api.conversations.rename);
  const removeConversation = useMutation(api.conversations.remove);

  // Authentification Clerk
  const { signOut } = useClerk();

  // État pour gérer le nombre de conversations à afficher
  const [displayLimit, setDisplayLimit] = useState(5);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // États pour gérer les menus contextuels
  const [menuOpenId, setMenuOpenId] = useState<Id<"conversations"> | null>(null);
  const [isRenaming, setIsRenaming] = useState<Id<"conversations"> | null>(null);
  const [newTitle, setNewTitle] = useState("");
  const [confirmDelete, setConfirmDelete] = useState<Id<"conversations"> | null>(null);
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false);

  // 📊 LOG: Compteur d'utilisation côté client
  console.log(`📋 [CLIENT] useEnhancedSidebar - displayLimit: ${displayLimit}`);

  // Récupération optimisée des données depuis Convex avec pagination côté serveur
  const conversationsData = useQuery(api.conversations.listForSidebar, { limit: displayLimit }) || { conversations: [], hasMore: false };

  const conversations = conversationsData.conversations;
  const hasMoreConversations = conversationsData.hasMore;

  // Références
  const conversationListRef = useRef<HTMLDivElement>(null);
  const renameInputRef = useRef<HTMLInputElement>(null);

  // Fonction pour afficher plus de conversations
  const handleShowMore = () => {
    if (isLoadingMore || !hasMoreConversations) return;

    // Sauvegarder la position de scroll actuelle
    const currentScrollTop = conversationListRef.current?.scrollTop || 0;

    setIsLoadingMore(true);

    // Augmenter la limite immédiatement pour éviter le clignotement
    setDisplayLimit(prev => prev + 5);

    // Restaurer la position de scroll après un court délai
    setTimeout(() => {
      if (conversationListRef.current) {
        conversationListRef.current.scrollTop = currentScrollTop;
      }
      setIsLoadingMore(false);
    }, 100);
  };

  // Fonction pour détecter le scroll et charger automatiquement plus de conversations
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // Si on est proche du bas (à 50px près) et qu'on n'est pas déjà en train de charger
    if (scrollHeight - scrollTop - clientHeight < 50 && hasMoreConversations && !isLoadingMore) {
      console.log(`📋 [CLIENT] Enhanced Auto-scroll triggered - scrollTop: ${scrollTop}`);
      handleShowMore();
    }
  };

  // Fonction pour ouvrir le menu contextuel d'une conversation
  const handleOpenConversationMenu = (e: React.MouseEvent, conversationId: Id<"conversations">) => {
    e.stopPropagation();
    setMenuOpenId(conversationId);
  };

  // Fonction pour fermer le menu contextuel d'une conversation
  const handleCloseConversationMenu = () => {
    setMenuOpenId(null);
  };

  // Fonction pour ouvrir/fermer le menu des paramètres
  const toggleSettingsMenu = () => {
    setSettingsMenuOpen(prev => !prev);
  };

  // Fonction pour fermer le menu des paramètres
  const handleCloseSettingsMenu = () => {
    setSettingsMenuOpen(false);
  };

  // Fonction pour commencer le renommage d'une conversation
  const handleStartRename = (conversationId: Id<"conversations">, title: string) => {
    console.log("[useEnhancedSidebar] handleStartRename called");
    console.log("[useEnhancedSidebar] conversationId:", conversationId);
    console.log("[useEnhancedSidebar] title:", title);

    setMenuOpenId(null);
    setIsRenaming(conversationId);
    setNewTitle(title);

    // Focus sur l'input après le rendu
    setTimeout(() => {
      console.log("[useEnhancedSidebar] setTimeout callback");
      console.log("[useEnhancedSidebar] renameInputRef.current:", renameInputRef.current);
      if (renameInputRef.current) {
        renameInputRef.current.focus();
      }
    }, 0);
  };

  // Fonction pour valider le renommage d'une conversation
  const handleRename = async (e: React.FormEvent, conversationId: Id<"conversations">) => {
    console.log("[useEnhancedSidebar] handleRename called");
    console.log("[useEnhancedSidebar] conversationId:", conversationId);
    console.log("[useEnhancedSidebar] newTitle:", newTitle);

    e.preventDefault();
    if (newTitle.trim()) {
      try {
        console.log("[useEnhancedSidebar] Calling renameConversation mutation");
        await renameConversation({ id: conversationId, title: newTitle.trim() });
        console.log("[useEnhancedSidebar] renameConversation mutation successful");
        setIsRenaming(null);
        setNewTitle("");
      } catch (error) {
        console.error("[useEnhancedSidebar] Erreur lors du renommage:", error);
      }
    } else {
      console.log("[useEnhancedSidebar] Empty title, canceling rename");
      setIsRenaming(null);
    }
  };

  // Fonction pour annuler le renommage d'une conversation
  const handleCancelRename = () => {
    setIsRenaming(null);
    setNewTitle("");
  };

  // Fonction pour confirmer la suppression d'une conversation
  const handleConfirmDelete = (conversationId: Id<"conversations">) => {
    console.log("[useEnhancedSidebar] handleConfirmDelete called");
    console.log("[useEnhancedSidebar] conversationId:", conversationId);

    setMenuOpenId(null);
    setConfirmDelete(conversationId);
  };

  // Fonction pour supprimer une conversation
  const handleDelete = async (conversationId: Id<"conversations">) => {
    console.log("[useEnhancedSidebar] handleDelete called");
    console.log("[useEnhancedSidebar] conversationId:", conversationId);

    try {
      console.log("[useEnhancedSidebar] Calling removeConversation mutation");
      await removeConversation({ id: conversationId });
      console.log("[useEnhancedSidebar] removeConversation mutation successful");
      setConfirmDelete(null);

      // Si la conversation supprimée est celle actuellement sélectionnée,
      // on sélectionne la première conversation disponible ou on passe en mode nouvelle conversation
      if (conversationId === currentConversationId) {
        console.log("[useEnhancedSidebar] Deleted conversation was the current one");
        const remainingConversations = conversations.filter(c => c._id !== conversationId);
        if (remainingConversations.length > 0) {
          console.log("[useEnhancedSidebar] Selecting first remaining conversation");
          onSelectConversation(remainingConversations[0]._id);
        } else {
          console.log("[useEnhancedSidebar] No conversations left, creating new one");
          onNewConversation();
        }
      }
    } catch (error) {
      console.error("[useEnhancedSidebar] Erreur lors de la suppression:", error);
    }
  };

  // Fonction pour annuler la suppression d'une conversation
  const handleCancelDelete = () => {
    setConfirmDelete(null);
  };

  // Fonction pour gérer la déconnexion
  const handleSignOut = () => {
    signOut();
  };

  // Effet pour focus sur l'input de renommage
  useEffect(() => {
    console.log("[useEnhancedSidebar] useEffect for focus triggered");
    console.log("[useEnhancedSidebar] isRenaming:", isRenaming);
    console.log("[useEnhancedSidebar] renameInputRef.current:", renameInputRef.current);

    if (isRenaming) {
      // Utiliser un délai pour s'assurer que l'élément est rendu
      setTimeout(() => {
        console.log("[useEnhancedSidebar] setTimeout callback for focus");
        console.log("[useEnhancedSidebar] renameInputRef.current (in timeout):", renameInputRef.current);
        if (renameInputRef.current) {
          renameInputRef.current.focus();
        }
      }, 100);
    }
  }, [isRenaming]);

  return {
    conversations,
    hasMoreConversations,
    isLoadingMore,
    conversationListRef,
    renameInputRef,
    handleShowMore,
    handleScroll,
    menuOpenId,
    isRenaming,
    newTitle,
    confirmDelete,
    settingsMenuOpen,
    handleOpenConversationMenu,
    handleCloseConversationMenu,
    toggleSettingsMenu,
    handleCloseSettingsMenu,
    handleStartRename,
    handleRename,
    setNewTitle,
    handleCancelRename,
    handleConfirmDelete,
    handleDelete,
    handleCancelDelete,
    handleSignOut
  };
};

export default useEnhancedSidebar;
