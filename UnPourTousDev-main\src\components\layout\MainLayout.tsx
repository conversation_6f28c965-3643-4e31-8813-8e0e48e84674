import React, { useState } from "react";
import { Outlet, useNavigate, useParams } from "react-router-dom";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import EnhancedSidebar from "../features/chat/EnhancedClaudeInterface/components/EnhancedSidebar";
import useEnhancedSidebar from "../features/chat/EnhancedClaudeInterface/hooks/useEnhancedSidebar";

/**
 * Composant MainLayout - Layout principal de l'application
 * Intègre la sidebar et utilise Outlet pour afficher le contenu des pages
 */
const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const { conversationId } = useParams<{ conversationId: string }>();

  // État pour gérer la rétraction de la sidebar (rétractée par défaut)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true);

  // État pour le bouton actif dans la sidebar
  const [activeButton, setActiveButton] = useState("chat");

  // Convertir l'ID de conversation en Id<"conversations"> si présent
  const currentConversationId = conversationId ? conversationId as Id<"conversations"> : null;

  // Fonction pour basculer l'état de la sidebar
  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  // Fonction pour naviguer vers la page de paramètres
  const navigateToSettings = () => {
    navigate("/settings");
  };

  // Fonction pour gérer le clic sur un bouton de la sidebar
  const handleSidebarClick = (button: string) => {
    setActiveButton(button);
  };

  // Fonction pour sélectionner une conversation
  const handleSelectConversation = (id: Id<"conversations">) => {
    navigate(`/c/${id}`);
  };

  // Fonction pour créer une nouvelle conversation
  const handleNewConversation = () => {
    navigate("/");
  };

  // Utiliser le hook pour la sidebar améliorée
  const {
    conversations: limitedConversations,
    hasMoreConversations,
    isLoadingMore,
    conversationListRef,
    renameInputRef,
    handleShowMore,
    handleScroll,
    menuOpenId,
    isRenaming,
    newTitle,
    confirmDelete,
    settingsMenuOpen,
    handleOpenConversationMenu,
    handleCloseConversationMenu,
    toggleSettingsMenu,
    handleCloseSettingsMenu,
    handleStartRename,
    handleRename,
    setNewTitle,
    handleCancelRename,
    handleConfirmDelete,
    handleDelete,
    handleCancelDelete,
    handleSignOut
  } = useEnhancedSidebar(currentConversationId, handleSelectConversation, handleNewConversation);

  return (
    <div className="claude-interface h-screen flex flex-col bg-claude-dark text-white">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar améliorée */}
        <EnhancedSidebar
          activeButton={activeButton}
          handleSidebarClick={handleSidebarClick}
          conversations={limitedConversations}
          currentConversationId={currentConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
          isCollapsed={isSidebarCollapsed}
          toggleSidebar={toggleSidebar}
          navigateToSettings={navigateToSettings}
          hasMoreConversations={hasMoreConversations}
          isLoadingMore={isLoadingMore}
          handleShowMore={handleShowMore}
          handleScroll={handleScroll}
          conversationListRef={conversationListRef}
          menuOpenId={menuOpenId}
          isRenaming={isRenaming}
          newTitle={newTitle}
          confirmDelete={confirmDelete}
          settingsMenuOpen={settingsMenuOpen}
          renameInputRef={renameInputRef}
          handleOpenConversationMenu={handleOpenConversationMenu}
          handleCloseConversationMenu={handleCloseConversationMenu}
          toggleSettingsMenu={toggleSettingsMenu}
          handleCloseSettingsMenu={handleCloseSettingsMenu}
          handleStartRename={handleStartRename}
          handleRename={handleRename}
          setNewTitle={setNewTitle}
          handleCancelRename={handleCancelRename}
          handleConfirmDelete={handleConfirmDelete}
          handleDelete={handleDelete}
          handleCancelDelete={handleCancelDelete}
          handleSignOut={handleSignOut}
        />

        {/* Contenu principal - utilise Outlet pour afficher les pages */}
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 w-full ${isSidebarCollapsed ? "ml-0" : ""}`}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
