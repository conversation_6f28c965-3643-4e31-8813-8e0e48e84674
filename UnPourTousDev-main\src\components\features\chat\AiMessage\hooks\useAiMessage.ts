import { useState, useEffect } from "react";
import { AiMessageProps } from "../types";
import { useModelInfo } from "../../../../../contexts/ModelsContext";

/**
 * Hook personnalisé pour gérer l'état et la logique d'un message IA
 */
export const useAiMessage = (message: AiMessageProps["message"]) => {
  // État pour l'animation du curseur
  const [showCursor, setShowCursor] = useState(true);

  // État pour afficher toutes les sources ou seulement les 3 premières
  const [showAllSources, setShowAllSources] = useState(false);

  // Utiliser le hook optimisé pour récupérer les informations du modèle
  const { displayName, isWebSearch, isReasoning } = useModelInfo(message.modelUsed || "");

  // Le nom à afficher (priorité au nom d'affichage du contexte)
  const modelName = displayName;

  // Utiliser les informations du contexte
  const isWebSearchModel = isWebSearch;
  const isAutoRouter = message.modelUsed === "openrouter/auto";

  // Détermine si le modèle supporte le raisonnement
  // Pour AutoRouter, on vérifie s'il y a du contenu de raisonnement réel
  // Pour les autres modèles, on utilise la propriété reasoning du contexte
  const isReasoningModel = isAutoRouter
    ? Boolean(message.reasoning_content) // AutoRouter : seulement si du raisonnement existe
    : isReasoning; // Autres modèles : selon leur capacité

  // Animation du curseur clignotant
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);

      return () => clearInterval(interval);
    } else {
      setShowCursor(false);
    }
  }, [message.isStreaming]);

  return {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter,
    isReasoningModel
  };
};
