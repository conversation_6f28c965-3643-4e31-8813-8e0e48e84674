import React, { createContext, useContext, ReactNode } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

// Types pour les modèles
interface Model {
  _id: string;
  name: string;
  modelId: string;
  provider: string;
  description: string;
  contextLength?: number;
  pricing?: {
    prompt: number;
    completion: number;
  };
  custom?: boolean;
  webSearch?: boolean;
  structuredOutput?: boolean;
  reasoning?: boolean;
  streaming?: boolean;
  chat?: boolean;
  enabled?: boolean;
}

// Interface du contexte
interface ModelsContextType {
  models: Model[];
  isLoading: boolean;
  getModelByModelId: (modelId: string) => Model | undefined;
  getModelDisplayName: (modelId: string) => string;
  getModelProvider: (modelId: string) => string;
  isWebSearchModel: (modelId: string) => boolean;
  isReasoningModel: (modelId: string) => boolean;
  isStreamingModel: (modelId: string) => boolean;
}

// Créer le contexte
const ModelsContext = createContext<ModelsContextType | undefined>(undefined);

// Props du provider
interface ModelsProviderProps {
  children: ReactNode;
}

/**
 * Provider qui charge et partage les données des modèles dans toute l'application
 */
export const ModelsProvider: React.FC<ModelsProviderProps> = ({ children }) => {
  // Charger les modèles une seule fois
  const models = useQuery(api.livemodels.list) || [];
  const isLoading = models.length === 0;

  // Fonction pour trouver un modèle par son modelId (avec correspondance flexible)
  const getModelByModelId = (modelId: string): Model | undefined => {
    if (!modelId) return undefined;

    // Recherche exacte d'abord
    let model = models.find(m => m.modelId === modelId);

    // Si pas trouvé, recherche par correspondance partielle
    if (!model) {
      // Extraire la partie après le "/" pour les modèles OpenAI/Anthropic/etc.
      const modelPart = modelId.split('/')[1] || modelId;

      model = models.find(m => {
        // Correspondance directe
        if (m.modelId === modelId) return true;

        // Correspondance partielle - le modelId contient une partie du nom technique
        if (modelId.includes(m.modelId)) return true;

        // Correspondance inverse - le nom technique contient le modelId
        if (m.modelId.includes(modelPart)) return true;

        // Correspondance par nom de base (ex: gpt-4o dans openai/gpt-4o-2024-08-06)
        const baseModelName = modelPart.split('-')[0]; // ex: "gpt" de "gpt-4o-2024-08-06"
        if (m.modelId.toLowerCase().includes(baseModelName.toLowerCase())) return true;

        return false;
      });
    }

    return model;
  };

  // Fonction pour obtenir le nom d'affichage d'un modèle
  const getModelDisplayName = (modelId: string): string => {
    if (modelId === "openrouter/auto") return "AutoSelect";
    
    const model = getModelByModelId(modelId);
    return model?.name || modelId;
  };

  // Fonction pour obtenir le provider d'un modèle
  const getModelProvider = (modelId: string): string => {
    const model = getModelByModelId(modelId);
    return model?.provider || modelId.split('/')[0] || 'unknown';
  };

  // Fonction pour vérifier si un modèle a la recherche web
  const isWebSearchModel = (modelId: string): boolean => {
    const model = getModelByModelId(modelId);
    return model?.webSearch === true;
  };

  // Fonction pour vérifier si un modèle a le raisonnement
  const isReasoningModel = (modelId: string): boolean => {
    const model = getModelByModelId(modelId);
    return model?.reasoning === true;
  };

  // Fonction pour vérifier si un modèle supporte le streaming
  const isStreamingModel = (modelId: string): boolean => {
    const model = getModelByModelId(modelId);
    return model?.streaming === true;
  };

  // Valeur du contexte
  const contextValue: ModelsContextType = {
    models,
    isLoading,
    getModelByModelId,
    getModelDisplayName,
    getModelProvider,
    isWebSearchModel,
    isReasoningModel,
    isStreamingModel,
  };

  return (
    <ModelsContext.Provider value={contextValue}>
      {children}
    </ModelsContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte des modèles
 */
export const useModels = (): ModelsContextType => {
  const context = useContext(ModelsContext);
  if (context === undefined) {
    throw new Error('useModels must be used within a ModelsProvider');
  }
  return context;
};

/**
 * Hook pour obtenir rapidement le nom d'affichage d'un modèle
 */
export const useModelDisplayName = (modelId: string): string => {
  const { getModelDisplayName } = useModels();
  return getModelDisplayName(modelId);
};

/**
 * Hook pour obtenir rapidement les informations d'un modèle
 */
export const useModelInfo = (modelId: string) => {
  const { getModelByModelId, getModelDisplayName, getModelProvider, isWebSearchModel, isReasoningModel, isStreamingModel } = useModels();
  
  const model = getModelByModelId(modelId);
  
  return {
    model,
    displayName: getModelDisplayName(modelId),
    provider: getModelProvider(modelId),
    isWebSearch: isWebSearchModel(modelId),
    isReasoning: isReasoningModel(modelId),
    isStreaming: isStreamingModel(modelId),
  };
};
