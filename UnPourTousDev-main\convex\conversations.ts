import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

export const list = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    const limit = args.limit || 100; // Limite par défaut raisonnable

    // Récupère les conversations de l'utilisateur avec limitation côté base de données
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("desc")
      .take(limit); // ✅ Limitation côté base de données

    // Ajouter les informations de modèle directement depuis conversation.modelId
    const conversationsWithLastModel = conversations.map((conversation) => {
      // Utiliser le modelId stocké dans la conversation au lieu de chercher dans les messages
      if (conversation.modelId) {
        // Extrait le fournisseur du modèle (ex: "openai/gpt-4" -> "openai")
        const provider = conversation.modelId.split('/')[0] || "default";

        return {
          ...conversation,
          lastModelUsed: conversation.modelId,
          lastModelProvider: provider
        };
      }

      return conversation;
    });

    return conversationsWithLastModel;
  },
});



export const rename = mutation({
  args: {
    id: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de la conversation
    const conversation = await ctx.db.get(args.id);
    if (!conversation) throw new Error("Conversation non trouvée");
    if (conversation.userId !== identity.subject) throw new Error("Non autorisé");

    // Renomme la conversation
    await ctx.db.patch(args.id, { title: args.title });
    return { success: true };
  },
});

export const remove = mutation({
  args: {
    id: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de la conversation
    const conversation = await ctx.db.get(args.id);
    if (!conversation) throw new Error("Conversation non trouvée");
    if (conversation.userId !== identity.subject) throw new Error("Non autorisé");

    // Supprime la conversation
    await ctx.db.delete(args.id);
    return { success: true };
  },
});



// Fonction pour créer une nouvelle conversation
export const create = mutation({
  args: {
    title: v.string(),
    modelId: v.optional(v.string()),
    usesAutoRouter: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Crée une nouvelle conversation
    const conversationId = await ctx.db.insert("conversations", {
      userId: identity.subject,
      title: args.title,
      modelId: args.modelId,
      usesAutoRouter: args.usesAutoRouter || false,
    });

    return conversationId;
  },
});

// Fonction pour récupérer une conversation par son ID
export const getById = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== identity.subject) {
      return null;
    }

    return conversation;
  },
});

// Version optimisée pour la sidebar avec vraie pagination côté base de données
export const listForSidebar = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return { conversations: [], hasMore: false };

    const limit = args.limit || 20; // Limite par défaut pour la sidebar

    // ✅ Récupère exactement le nombre demandé + 1 pour savoir s'il y en a plus
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("desc")
      .take(limit + 1); // +1 pour détecter s'il y a plus de conversations

    // Séparer les conversations à retourner et détecter s'il y en a plus
    const hasMore = conversations.length > limit;
    const conversationsToReturn = hasMore ? conversations.slice(0, limit) : conversations;

    // Ajouter les informations de modèle directement depuis conversation.modelId
    const conversationsWithLastModel = conversationsToReturn.map((conversation) => {
      // Utiliser le modelId stocké dans la conversation au lieu de chercher dans les messages
      if (conversation.modelId) {
        // Extrait le fournisseur du modèle (ex: "openai/gpt-4" -> "openai")
        const provider = conversation.modelId.split('/')[0] || "default";

        return {
          ...conversation,
          lastModelUsed: conversation.modelId,
          lastModelProvider: provider
        };
      }

      return conversation;
    });

    return {
      conversations: conversationsWithLastModel,
      hasMore: hasMore,
    };
  },
});

