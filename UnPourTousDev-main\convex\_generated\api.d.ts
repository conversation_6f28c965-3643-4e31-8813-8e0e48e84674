/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as ai from "../ai.js";
import type * as auth from "../auth.js";
import type * as config from "../config.js";
import type * as conversations from "../conversations.js";
import type * as http from "../http.js";
import type * as internal_messages from "../internal/messages.js";
import type * as internal_models from "../internal/models.js";
import type * as internal_openrouter from "../internal/openrouter.js";
import type * as internal_titleGenerator from "../internal/titleGenerator.js";
import type * as livemodels from "../livemodels.js";
import type * as messages from "../messages.js";
import type * as models from "../models.js";
import type * as notdiamond from "../notdiamond.js";
import type * as openrouter from "../openrouter.js";
import type * as semanticrouter from "../semanticrouter.js";
import type * as streaming from "../streaming.js";
import type * as userPreferences from "../userPreferences.js";
import type * as users from "../users.js";
import type * as utils from "../utils.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  ai: typeof ai;
  auth: typeof auth;
  config: typeof config;
  conversations: typeof conversations;
  http: typeof http;
  "internal/messages": typeof internal_messages;
  "internal/models": typeof internal_models;
  "internal/openrouter": typeof internal_openrouter;
  "internal/titleGenerator": typeof internal_titleGenerator;
  livemodels: typeof livemodels;
  messages: typeof messages;
  models: typeof models;
  notdiamond: typeof notdiamond;
  openrouter: typeof openrouter;
  semanticrouter: typeof semanticrouter;
  streaming: typeof streaming;
  userPreferences: typeof userPreferences;
  users: typeof users;
  utils: typeof utils;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
