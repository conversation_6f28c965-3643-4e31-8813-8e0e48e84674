import { useState, useRef, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../../../convex/_generated/api";
import { Id } from "../../../../../convex/_generated/dataModel";
import { useClerk } from "@clerk/clerk-react";
import { calculateMenuPosition } from "../../../ContextMenu";

/**
 * Hook personnalisé pour gérer l'état et la logique de la sidebar
 */
export const useSidebar = (
  currentConversationId: Id<"conversations"> | null | "new",
  onSelectConversation: (id: Id<"conversations">) => void,
  onNewConversation: () => void
) => {
  // État pour gérer le nombre de conversations à afficher
  const [displayLimit, setDisplayLimit] = useState(5);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // 📊 LOG: Compteur d'utilisation côté client
  console.log(`📋 [CLIENT] useSidebar - displayLimit: ${displayLimit}`);

  // Récupération optimisée des données depuis Convex avec pagination côté serveur
  const conversationsData = useQuery(api.conversations.listForSidebar, { limit: displayLimit }) || { conversations: [], hasMore: false };

  const conversations = conversationsData.conversations;
  const hasMoreConversations = conversationsData.hasMore;

  // Mutations Convex
  const renameConversation = useMutation(api.conversations.rename);
  const removeConversation = useMutation(api.conversations.remove);

  // Authentification Clerk
  const { signOut } = useClerk();

  // États pour gérer le menu contextuel des conversations
  const [menuOpen, setMenuOpen] = useState<Id<"conversations"> | null>(null);
  const [isRenaming, setIsRenaming] = useState<Id<"conversations"> | null>(null);
  const [newTitle, setNewTitle] = useState("");
  const [confirmDelete, setConfirmDelete] = useState<Id<"conversations"> | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0, openUpward: false });

  // État pour gérer la rétraction de la sidebar
  const [isCollapsed, setIsCollapsed] = useState(false);

  // État pour gérer le menu des paramètres
  const [settingsMenuOpen, setSettingsMenuOpen] = useState(false);

  // Références
  const renameInputRef = useRef<HTMLInputElement>(null);
  const conversationListRef = useRef<HTMLDivElement>(null);

  // Fonction pour afficher plus de conversations
  const handleShowMore = () => {
    if (isLoadingMore || !hasMoreConversations) return;

    // Sauvegarder la position de scroll actuelle
    const currentScrollTop = conversationListRef.current?.scrollTop || 0;

    setIsLoadingMore(true);

    // Augmenter la limite immédiatement pour éviter le clignotement
    setDisplayLimit(prev => prev + 5);

    // Restaurer la position de scroll après un court délai
    setTimeout(() => {
      if (conversationListRef.current) {
        conversationListRef.current.scrollTop = currentScrollTop;
      }
      setIsLoadingMore(false);
    }, 100);
  };

  // Fonction pour gérer la déconnexion
  const handleSignOut = () => {
    signOut();
  };

  // Fonction pour ouvrir ou fermer le menu contextuel
  const handleOpenMenu = (e: React.MouseEvent, conversationId: Id<"conversations">) => {
    e.stopPropagation();
    e.preventDefault();

    // Si le menu est déjà ouvert pour cette conversation, le ferme
    if (menuOpen === conversationId) {
      setTimeout(() => {
        setMenuOpen(null);
      }, 0);
      return;
    }

    // Ferme les autres menus qui pourraient être ouverts
    setSettingsMenuOpen(false);

    // Calcule la position du menu
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const position = calculateMenuPosition(rect, 200, 160, isCollapsed); // hauteur: 200px, largeur: 160px, état collapsed
    setMenuPosition(position);

    // Ouvre le menu pour cette conversation
    setMenuOpen(conversationId);
  };

  // Fonction pour commencer le renommage
  const handleStartRename = (e: React.MouseEvent, conversationId: Id<"conversations">, title: string) => {
    e.stopPropagation();
    setMenuOpen(null);
    setIsRenaming(conversationId);
    setNewTitle(title);
  };

  // Fonction pour valider le renommage
  const handleRename = async (e: React.FormEvent, conversationId: Id<"conversations">) => {
    e.preventDefault();
    if (newTitle.trim()) {
      try {
        await renameConversation({ id: conversationId, title: newTitle.trim() });
        setIsRenaming(null);
        setNewTitle("");
      } catch (error) {
        console.error("Erreur lors du renommage:", error);
      }
    }
  };

  // Fonction pour demander confirmation de suppression
  const handleConfirmDelete = (e: React.MouseEvent, conversationId: Id<"conversations">) => {
    e.stopPropagation();
    setMenuOpen(null);
    setConfirmDelete(conversationId);
  };

  // Fonction pour supprimer une conversation
  const handleDelete = async (conversationId: Id<"conversations">) => {
    try {
      await removeConversation({ id: conversationId });
      setConfirmDelete(null);

      // Si la conversation supprimée est celle actuellement sélectionnée,
      // on sélectionne la première conversation disponible ou on passe en mode nouvelle conversation
      if (conversationId === currentConversationId) {
        const remainingConversations = conversations.filter((c: any) => c._id !== conversationId);
        if (remainingConversations.length > 0) {
          onSelectConversation(remainingConversations[0]._id);
        } else {
          onNewConversation();
        }
      }
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
    }
  };

  // Fonction pour ouvrir ou fermer le menu des paramètres
  const handleOpenSettingsMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // Si le menu est déjà ouvert, le ferme
    if (settingsMenuOpen) {
      setSettingsMenuOpen(false);
      return;
    }

    // Ferme les autres menus qui pourraient être ouverts
    setMenuOpen(null);

    // Calcule la position du menu
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const position = calculateMenuPosition(rect, 120, 160, isCollapsed); // hauteur: 120px, largeur: 160px (w-40), état collapsed
    setMenuPosition(position);

    // Ouvre le menu des paramètres
    setSettingsMenuOpen(true);
  };

  // Fonction pour basculer l'état de la sidebar
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Fonction pour détecter le scroll et charger automatiquement plus de conversations
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // Si on est proche du bas (à 50px près) et qu'on n'est pas déjà en train de charger
    if (scrollHeight - scrollTop - clientHeight < 50 && hasMoreConversations && !isLoadingMore) {
      console.log(`📋 [CLIENT] Auto-scroll triggered - scrollTop: ${scrollTop}`);
      handleShowMore();
    }
  };

  // Effet pour initialiser le gestionnaire de scroll
  useEffect(() => {
    // Rien à faire ici, le gestionnaire de scroll est attaché via la prop onScroll
  }, []);

  return {
    // États
    conversations,
    hasMoreConversations,
    menuOpen,
    isRenaming,
    newTitle,
    confirmDelete,
    menuPosition,
    isCollapsed,
    settingsMenuOpen,
    renameInputRef,
    conversationListRef,
    isLoadingMore,

    // Setters
    setNewTitle,
    setConfirmDelete,
    setMenuOpen,
    setSettingsMenuOpen,

    // Handlers
    handleSignOut,
    handleOpenMenu,
    handleStartRename,
    handleRename,
    handleConfirmDelete,
    handleDelete,
    handleOpenSettingsMenu,
    handleShowMore,
    handleScroll,
    toggleSidebar
  };
};
